import csv
import json
import argparse
import requests
import time
from typing import List, Dict, Tuple

# Configuration for local LM Studio API
API_URL = "http://192.168.0.102:1234/v1/chat/completions"
MODEL_NAME = "mistral-7b-instruct-v0.3"

def read_paa_questions(filename: str) -> List[str]:
    """Read PAA questions from CSV file."""
    questions = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            questions.append(row['original_question'])
    return questions

def read_valid_categories(filename: str) -> List[Tuple[str, str]]:
    """Read valid parent-child category pairs from CSV file."""
    categories = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            parent = row['parent_cats'].strip()
            child = row['sub_cats'].strip()
            categories.append((parent, child))
    return categories

def get_business_context() -> str:
    """Return business context for AgentiveAI."""
    return """
AgentiveAI is a comprehensive no-code AI agent platform that enables businesses to deploy intelligent, industry-specific AI agents in just 5 minutes. Our platform combines advanced natural language processing, deep document understanding, and real-time integrations to create AI agents that don't just answer questions—they understand context, remember interactions, and take meaningful actions that drive business results.

With 9 specialized AI agent types, enterprise-grade security, and seamless integrations with major e-commerce platforms, AgentiveAI serves as a complete AI ecosystem that transforms how businesses engage with customers, support teams, and prospects across multiple touchpoints.

Our specialized AI agents include:
1. E-Commerce Agent - Real-time product catalog integration, inventory management, order tracking, cart abandonment recovery, personal shopping recommendations
2. Customer Support Agent - 80% support ticket deflection, smart escalation protocols, 24/7 availability, policy and FAQ expertise
3. Sales & Lead Generation Agent - Qualified lead capture and scoring, natural conversation flows, value-building through education, instant lead delivery
4. Real Estate Agent - Property matching and qualification, automated viewing scheduling, market insights and analysis
5. Finance Agent - Loan pre-qualification processes, financial education and guidance, compliance-ready conversations
6. Education Agent - AI-powered tutoring capabilities, course navigation and progress tracking, homework assistance, student struggle alerts
7. HR & Internal Agent - Employee policy expertise, confidential inquiry handling, smart issue escalation, onboarding and training support
8. Training & Onboarding Agent - Guided new hire experiences, progress monitoring and reporting, manager alerts and notifications, resource delivery and tracking
9. Custom Agent - Fully customizable conversation flows, industry-agnostic capabilities, complete behavioral control, advanced integration options

Our platform features include:
- Visual Builder & Customization with WYSIWYG editor and real-time preview
- Knowledge Ingestion supporting website scraping and document upload
- Smart Engagement Features with behavioral triggers and personalized messaging
- Hosted Pages with password-protected AI experiences
- AI Courses with interactive course creation
- Smart Triggers for exit intent capture and behavioral pattern recognition
- Assistant Agent for conversation monitoring and automated follow-ups

We integrate with major e-commerce platforms including Shopify and WooCommerce, as well as webhook-based integrations.
"""

def get_categories_context(valid_categories: List[Tuple[str, str]]) -> str:
    """Return exact category pairs for strict categorization."""
    context = "You MUST choose from one of these EXACT parent-child category pairs:\n\n"

    for i, (parent, child) in enumerate(valid_categories, 1):
        context += f"{i}. Parent: \"{parent}\" | Child: \"{child}\"\n"

    context += "\nYou are REQUIRED to select one of these exact pairs. Do not create new categories or modify the names."
    return context

def choose_fallback_category(question: str, valid_categories: List[Tuple[str, str]]) -> Tuple[str, str]:
    """Choose an appropriate fallback category based on question keywords."""
    question_lower = question.lower()

    # Define keyword mappings to category pairs
    keyword_mappings = [
        # E-commerce related
        (['ecommerce', 'e-commerce', 'shopify', 'woocommerce', 'store', 'product', 'cart', 'checkout', 'recommendation'],
         'AI for E-commerce'),

        # Sales related
        (['sales', 'lead', 'conversion', 'crm', 'pipeline', 'prospect', 'qualify'],
         'AI for Sales & Lead Generation'),

        # Professional services
        (['appointment', 'booking', 'client', 'service', 'consultation', 'proposal', 'quote'],
         'AI for Professional Services'),

        # Industry solutions
        (['real estate', 'finance', 'healthcare', 'legal', 'manufacturing', 'property', 'loan'],
         'AI for Industry Solutions'),

        # Education
        (['education', 'learning', 'course', 'student', 'training', 'teach'],
         'AI for Education & Training'),

        # Internal operations
        (['hr', 'employee', 'internal', 'it support', 'compliance', 'security', 'onboarding'],
         'AI for Internal Operations'),

        # Agency/Reseller
        (['agency', 'reseller', 'white label', 'pricing', 'package'],
         'Agency & Reseller Success'),
    ]

    # Find matching parent category
    for keywords, target_parent in keyword_mappings:
        if any(keyword in question_lower for keyword in keywords):
            # Find the first category pair with this parent
            for parent, child in valid_categories:
                if parent == target_parent:
                    return parent, child

    # If no keywords match, return the first category as ultimate fallback
    return valid_categories[0]

def query_local_llm(prompt: str, max_tokens: int = 500) -> str:
    """Query the local LLM via LM Studio API."""
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7,
        "max_tokens": max_tokens
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=data)
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            print(f"Error: API returned status code {response.status_code}")
            return "Error processing request"
    except Exception as e:
        print(f"Error connecting to LLM: {e}")
        return "Error connecting to LLM"

def categorize_question_strict(question: str, valid_categories: List[Tuple[str, str]]) -> Dict:
    """Strictly categorize a single PAA question using only valid category pairs."""
    prompt = f"""
You are an AI assistant helping to categorize People Also Ask (PAA) questions for blog content creation.

Business Context:
{get_business_context()}

CRITICAL INSTRUCTIONS:
You MUST select from these EXACT parent-child category pairs. DO NOT create new categories or modify names.

{get_categories_context(valid_categories)}

EXAMPLES of correct responses:
- For a question about e-commerce product recommendations: "AI for E-commerce" + "Product Discovery & Recommendations"
- For a question about sales automation: "AI for Sales & Lead Generation" + "24/7 Sales Automation"
- For a question about appointment booking: "AI for Professional Services" + "Appointment & Booking Systems"

Question: "{question}"

You MUST respond with this EXACT JSON format using only the category names from the numbered list above:
{{
  "parent_category": "Copy exact parent name from list above",
  "child_category": "Copy exact child name from list above",
  "content_focus": "Brief suggestion focusing on AgentiveAI's solutions"
}}

REMEMBER: Only use the exact category names from the numbered list. Do not invent new categories."""

    response = query_local_llm(prompt, max_tokens=400)

    # Try to extract JSON from response
    try:
        # Find the first { and last } to extract JSON
        start = response.find('{')
        end = response.rfind('}') + 1
        if start != -1 and end > start:
            json_str = response[start:end]
            result = json.loads(json_str)

            # Validate that the categories are in our valid list
            parent = result.get("parent_category", "").strip()
            child = result.get("child_category", "").strip()

            if (parent, child) in valid_categories:
                return result
            else:
                # If not valid, choose a more appropriate fallback based on question content
                fallback_parent, fallback_child = choose_fallback_category(question, valid_categories)
                return {
                    "parent_category": fallback_parent,
                    "child_category": fallback_child,
                    "content_focus": f"Auto-assigned based on question content. Original AI response used invalid categories."
                }
        else:
            # Fallback if JSON parsing fails
            fallback_parent, fallback_child = choose_fallback_category(question, valid_categories)
            return {
                "parent_category": fallback_parent,
                "child_category": fallback_child,
                "content_focus": f"Auto-assigned based on question content. Could not parse AI response."
            }
    except json.JSONDecodeError:
        # Fallback if JSON parsing fails
        fallback_parent, fallback_child = choose_fallback_category(question, valid_categories)
        return {
            "parent_category": fallback_parent,
            "child_category": fallback_child,
            "content_focus": f"Auto-assigned based on question content. JSON parsing error."
        }

def process_questions(questions: List[str], valid_categories: List[Tuple[str, str]], test_mode: bool = False) -> List[Dict]:
    """Process a list of questions, with optional test mode."""
    results = []

    # Limit to first 5 questions in test mode
    if test_mode:
        questions = questions[:5]
        print(f"Running in test mode. Processing first {len(questions)} questions...")
    else:
        print(f"Processing {len(questions)} questions...")

    for i, question in enumerate(questions):
        print(f"Processing question {i+1}/{len(questions)}: {question[:50]}...")

        result = categorize_question_strict(question, valid_categories)
        result["original_question"] = question

        results.append(result)

        # Add a small delay to avoid overwhelming the API
        time.sleep(0.5)

        # Print result in test mode for verification
        if test_mode:
            print(f"Result: {result}")

    return results

def save_results_to_csv(results: List[Dict], filename: str):
    """Save results to a CSV file."""
    fieldnames = [
        "original_question", "parent_category",
        "child_category", "content_focus"
    ]

    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()

        for result in results:
            writer.writerow(result)

    print(f"Results saved to {filename}")

def main():
    """Main function to run the PAA categorizer."""
    parser = argparse.ArgumentParser(description="Strictly categorize unmatched PAA questions using exact category pairs")
    parser.add_argument("--test", action="store_true", help="Run in test mode with first 5 questions")
    parser.add_argument("--input", default="Output_unmatched - Sheet1.csv", help="Input CSV file with unmatched questions")
    parser.add_argument("--categories", default="categories - Sheet1.csv", help="CSV file with valid category pairs")
    parser.add_argument("--output", default="rematched_paa_questions.csv", help="Output CSV file")

    args = parser.parse_args()

    # Read valid categories from CSV
    print("Loading valid category pairs...")
    valid_categories = read_valid_categories(args.categories)
    print(f"Loaded {len(valid_categories)} valid category pairs")

    # Read questions from CSV
    print("Loading unmatched questions...")
    questions = read_paa_questions(args.input)
    print(f"Loaded {len(questions)} questions to categorize")

    # Process questions
    results = process_questions(questions, valid_categories, args.test)

    # Save results to CSV
    save_results_to_csv(results, args.output)

    print("Processing complete!")

if __name__ == "__main__":
    main()
