import csv
import json
import argparse
import requests
import time
from typing import List, Dict, Tuple

# Configuration for local LM Studio API
API_URL = "http://192.168.0.102:1234/v1/chat/completions"
MODEL_NAME = "mistral-7b-instruct-v0.3"

def read_paa_questions(filename: str) -> List[str]:
    """Read PAA questions from CSV file."""
    questions = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            questions.append(row['PAA_Question'])
    return questions

def get_business_context() -> str:
    """Return business context for AgentiveAI."""
    return """
AgentiveAI is a comprehensive no-code AI agent platform that enables businesses to deploy intelligent, industry-specific AI agents in just 5 minutes. Our platform combines advanced natural language processing, deep document understanding, and real-time integrations to create AI agents that don't just answer questions—they understand context, remember interactions, and take meaningful actions that drive business results.

With 9 specialized AI agent types, enterprise-grade security, and seamless integrations with major e-commerce platforms, AgentiveAI serves as a complete AI ecosystem that transforms how businesses engage with customers, support teams, and prospects across multiple touchpoints.

Our specialized AI agents include:
1. E-Commerce Agent - Real-time product catalog integration, inventory management, order tracking, cart abandonment recovery, personal shopping recommendations
2. Customer Support Agent - 80% support ticket deflection, smart escalation protocols, 24/7 availability, policy and FAQ expertise
3. Sales & Lead Generation Agent - Qualified lead capture and scoring, natural conversation flows, value-building through education, instant lead delivery
4. Real Estate Agent - Property matching and qualification, automated viewing scheduling, market insights and analysis
5. Finance Agent - Loan pre-qualification processes, financial education and guidance, compliance-ready conversations
6. Education Agent - AI-powered tutoring capabilities, course navigation and progress tracking, homework assistance, student struggle alerts
7. HR & Internal Agent - Employee policy expertise, confidential inquiry handling, smart issue escalation, onboarding and training support
8. Training & Onboarding Agent - Guided new hire experiences, progress monitoring and reporting, manager alerts and notifications, resource delivery and tracking
9. Custom Agent - Fully customizable conversation flows, industry-agnostic capabilities, complete behavioral control, advanced integration options

Our platform features include:
- Visual Builder & Customization with WYSIWYG editor and real-time preview
- Knowledge Ingestion supporting website scraping and document upload
- Smart Engagement Features with behavioral triggers and personalized messaging
- Hosted Pages with password-protected AI experiences
- AI Courses with interactive course creation
- Smart Triggers for exit intent capture and behavioral pattern recognition
- Assistant Agent for conversation monitoring and automated follow-ups

We integrate with major e-commerce platforms including Shopify and WooCommerce, as well as webhook-based integrations.
"""

def get_categories_context() -> str:
    """Return category context for blog categorization."""
    return """
Parent Category 1: AI for E-commerce
Child Categories:
- Platform Integrations: Technical setup guides for e-commerce platforms, installation speed, feature availability
- Cart Recovery & Conversion: Reducing cart abandonment, increasing conversion rates, psychological triggers
- Product Discovery & Recommendations: AI-powered product matching, cross-selling, upselling strategies
- Customer Service Automation: Common customer service challenges, response time improvements, support cost reductions
- Peak Season Scaling: Preparing for high-traffic periods, performance optimization, maintaining service quality

Parent Category 2: AI for Sales & Lead Generation
Child Categories:
- Lead Qualification & Scoring: Identifying high-intent visitors, qualification criteria, scoring methodologies
- Conversion Optimization: Turning website visitors into qualified leads, landing page optimization
- 24/7 Sales Automation: Capturing leads outside business hours, automated qualifying, meeting scheduling
- Pipeline Management: Integrating AI chat data with CRM systems, data synchronization, follow-up automation
- Sales Team Training: Leveraging AI chat insights, objection handling, conversation analysis

Parent Category 3: AI for Professional Services
Child Categories:
- Client Onboarding Automation: Streamlining client intake process, reducing administrative work
- Appointment & Booking Systems: Automating scheduling processes, reducing booking friction
- Service Delivery Support: Enhancing ongoing client communication, project management automation
- Proposal & Quote Generation: Automating proposal creation process, improving quote-to-close conversion
- Client Retention Strategies: Maintaining long-term client relationships, identifying upselling opportunities

Parent Category 4: AI for Industry Solutions
Child Categories:
- Real Estate Automation: Property matching, buyer qualification, showing coordination
- Financial Services AI: Loan pre-qualification, investment guidance, regulatory compliance
- Healthcare & Wellness: HIPAA compliance, patient interaction automation, appointment management
- Legal & Professional: Legal consultation processes, document collection, confidentiality
- Manufacturing & B2B: Complex B2B sales processes, technical specifications, distributor management

Parent Category 5: AI for Education & Training
Child Categories:
- Interactive Course Creation: Building engaging, AI-enhanced courses with multimedia
- Student Engagement & Support: 24/7 student support, personalized learning assistance
- Corporate Learning Solutions: AI-powered training systems, employee development
- Creator Economy Tools: Helping course creators monetize expertise
- Learning Analytics: Using data to improve educational outcomes

Parent Category 6: AI for Internal Operations
Child Categories:
- HR Automation: Reducing HR workload, automated employee support
- IT & Technical Support: Automating technical support processes, reducing ticket volume
- Employee Onboarding: Streamlining new hire processes, improving onboarding experience
- Communication & Collaboration: Improving internal communication efficiency
- Compliance & Security: Automating compliance tracking, security policy enforcement

Parent Category 7: Agency & Reseller Success
Child Categories:
- White-Label Solutions: Customizing and branding AI solutions for clients
- Client Acquisition Strategies: Winning new clients, positioning AI services
- Service Delivery Excellence: Delivering high-quality AI implementations
- Pricing & Packaging: Developing profitable pricing strategies
- Scaling Agency Operations: Growing AI service offerings, managing client volume
"""

def query_local_llm(prompt: str, max_tokens: int = 500) -> str:
    """Query the local LLM via LM Studio API."""
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7,
        "max_tokens": max_tokens
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=data)
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            print(f"Error: API returned status code {response.status_code}")
            return "Error processing request"
    except Exception as e:
        print(f"Error connecting to LLM: {e}")
        return "Error connecting to LLM"

def filter_and_categorize_question(question: str) -> Dict:
    """Filter and categorize a single PAA question."""
    prompt = f"""
You are an AI assistant helping to filter and categorize People Also Ask (PAA) questions for blog content creation. 

Business Context:
{get_business_context()}

Blog Categories:
{get_categories_context()}

Task:
1. Determine if this question is relevant to AgentiveAI's business and suitable for a blog article
2. If relevant, categorize it into the most appropriate parent and child category
3. Provide a confidence score (1-10) for your categorization
4. Suggest a content focus for the article

Question: "{question}"

Please respond in the following JSON format:
{{
  "relevant": true/false,
  "parent_category": "Category name" or null,
  "child_category": "Category name" or null,
  "confidence_score": number 1-10,
  "content_focus": "Brief suggestion on how to approach this topic as a blog article"
}}
"""
    
    response = query_local_llm(prompt, max_tokens=300)
    
    # Try to extract JSON from response
    try:
        # Find the first { and last } to extract JSON
        start = response.find('{')
        end = response.rfind('}') + 1
        if start != -1 and end > start:
            json_str = response[start:end]
            return json.loads(json_str)
        else:
            return {
                "relevant": False,
                "parent_category": None,
                "child_category": None,
                "confidence_score": 0,
                "content_focus": "Could not process response"
            }
    except json.JSONDecodeError:
        return {
            "relevant": False,
            "parent_category": None,
            "child_category": None,
            "confidence_score": 0,
            "content_focus": f"Error parsing response: {response}"
        }

def process_questions(questions: List[str], test_mode: bool = False) -> List[Dict]:
    """Process a list of questions, with optional test mode."""
    results = []
    
    # Limit to first 5 questions in test mode
    if test_mode:
        questions = questions[:5]
        print(f"Running in test mode. Processing first {len(questions)} questions...")
    else:
        print(f"Processing {len(questions)} questions...")
    
    for i, question in enumerate(questions):
        print(f"Processing question {i+1}/{len(questions)}: {question[:50]}...")
        
        result = filter_and_categorize_question(question)
        result["original_question"] = question
        
        results.append(result)
        
        # Add a small delay to avoid overwhelming the API
        time.sleep(0.5)
        
        # Print result in test mode for verification
        if test_mode:
            print(f"Result: {result}")
    
    return results

def save_results_to_csv(results: List[Dict], filename: str):
    """Save results to a CSV file."""
    fieldnames = [
        "original_question", "relevant", "parent_category", 
        "child_category", "confidence_score", "content_focus"
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in results:
            writer.writerow(result)
    
    print(f"Results saved to {filename}")

def main():
    """Main function to run the PAA categorizer."""
    parser = argparse.ArgumentParser(description="Categorize PAA questions for blog content")
    parser.add_argument("--test", action="store_true", help="Run in test mode with first 5 questions")
    parser.add_argument("--input", default="Agentive PAA - Results.csv", help="Input CSV file")
    parser.add_argument("--output", default="categorized_paa_questions.csv", help="Output CSV file")
    
    args = parser.parse_args()
    
    # Read questions from CSV
    questions = read_paa_questions(args.input)
    
    # Process questions
    results = process_questions(questions, args.test)
    
    # Save results to CSV
    save_results_to_csv(results, args.output)
    
    print("Processing complete!")

if __name__ == "__main__":
    main()
